.page {
   
    padding: 1cm;
    border: 1px #D3D3D3 solid;
    border-radius: 5px;
    background: white;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
    display: flex;
    width: 21cm;
    margin: 1cm auto;
}

.page-size2{
	padding: 1cm;
    border: 1px #D3D3D3 solid;
    border-radius: 5px;
    background: white;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
    display: flex;
    margin: 1cm auto;
}

.tr_header{
	border-top: 1px solid black;
  	border-bottom: 1px solid black;
}
.tr_total{
	font-weight: bold;
	border-top: 1px solid black;
}
.border_top{
    border-top: 1px solid black;
}
.card-header{
	width: 100%;
    display: inline-block;
}
#accordion{
  width:100%;
}
.page hr{
	margin-top: 5px !important;
    margin-bottom: 5px !important;
}
table{
  width:100%;
}

.col-md-12, .col-md-2, .col-md-1{
	padding-right: 0px !important;
}

.total_amount{
    padding-left: 10px;
	text-align: right;
}

.th_total{
	text-align: right;
	width:16.66666667%;
	border-left: 1px solid black;
	border-top: 1px solid black;
}
.th_total_2{
    text-align: right;
    width:21.666667%;
    border-left: 1px solid black;
    border-top: 1px solid black;
}

th{
    border-top: 1px solid black;
    border-bottom: 1px solid black;
}

.label_total{
	width: 16.66666667%;
    text-align: right;
}

.text-center{
	text-align: center !important;
}

.th_total_width_auto{
    text-align: right;
    border-left: 1px solid black;
    border-top: 1px solid black;
}

.btn-group.pull-right.mtop25>.btn{
    margin-right: 15px;
}

.no-margin-top-20{
    margin-top: -20px;
}

.no-margin-left-24{
    margin-left: -24px;
}

.border-top{
    border-top: 1px solid black;
}

.border-bottom{
    border-bottom: 1px solid black;
}

.text-bold{
    font-weight: bold;
}

#accordion{
    color: black !important;
    font-weight: 450;
}


#DivIdToPrint{
    color: black !important;
}


tr.expanded{
    font-weight: 550;
}

.text-default-bl{
    color: black !important;
}

tr.tr_total>td{
    padding-bottom: 15px;
}
