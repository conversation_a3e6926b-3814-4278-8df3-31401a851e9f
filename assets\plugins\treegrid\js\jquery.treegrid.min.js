!function(e){function t(t){var n=e(this),a=e.extend({},e.fn.treegrid.defaults,t);return n.each(function(){var t=e(this);t.data("treegrid-settings",a),t.on("click",">tbody>tr>td:first-child>.treegrid-container>.treegrid-expander",p).on("mousedown",">tbody>tr>td:first-child>.treegrid-container",v),f(S.getRoots.call(t),1)})}function n(t){var n=t.closest("table").data("treegrid-settings");n.onExpand.call(t)&&(t.addClass("expanded"),t.find(">td:first>.treegrid-container>.treegrid-expander").removeClass("treegrid-expander-collapsed").addClass("treegrid-expander-expanded"),o(t).not(t).each(function(){var t=e(this);h(t)?t.hide():t.show()}))}function a(e){var t=e.closest("table").data("treegrid-settings");t.onCollapse.call(e)&&(e.removeClass("expanded"),e.find(">td:first>.treegrid-container>.treegrid-expander").removeClass("treegrid-expander-expanded").addClass("treegrid-expander-collapsed"),o(e).not(e).hide())}function r(t,n){var a,r=o(t).last().next(),i=[],d=s(t),l=t;"TABLE"!==l.prop("tagName")&&(l=l.closest("table")),a=l.find(">thead>tr,>tbody>tr").first().find(">th,>td").length,e.each(n,function(t,n){var o=e(n);if(!l.find(">tbody>tr.treegrid-"+s(o)).length){null!==d&&o.addClass("treegrid-parent-"+d);var c=o.find(">td").length;if(a>c)for(var t=o.find(">td").length;a>t;t++)o.append("<td>");else c>a&&o.find(">td").eq(a-1).nextAll().remove();r.length?o.insertBefore(r):l.find(">tbody").append(o),i.push(o[0])}}),i=e(i),null===d&&f(i),f(t,t.data("treegrid-depth"));var c=t.closest("table").data("treegrid-settings");c.onAdd.call(t,i)}function i(e){return e.parent().find(">.treegrid-"+l(e))}function d(e){return e.parent().find(">.treegrid-parent-"+s(e))}function o(t){var n=t;return"TR"!==t.prop("tagName")?n.not(t):(d(t).each(function(){n=n.add(o(e(this)))}),n)}function s(e){var t=/treegrid-([A-Fa-f0-9_]+)/;return t.test(e.attr("class"))?t.exec(e.attr("class"))[1]:null}function l(e){var t=/treegrid-parent-([A-Fa-f0-9_]+)/;return t.test(e.attr("class"))?t.exec(e.attr("class"))[1]:null}function c(e){return e.data("count")&&!e.hasClass("expanded")}function u(e){return e.data("count")&&e.hasClass("expanded")}function f(t,n,a){void 0===n&&(n=1),t.each(function(){var t=e(this).data("treegrid-depth",n),r=d(t),i=r.length;void 0===t.data("count")||t.data("loaded")||t.data("count")==i?t.data("loadNeeded")||(t.data({loaded:!0,count:i}),i&&a&&t.addClass("expanded")):t.data("loadNeeded",!0),$td=t.find(">td:first"),$container=$td.find(">.treegrid-container"),0===$container.length&&($container=e('<div class="treegrid-container">').html($td.html()),$td.html("").append($container)),$container.find(".treegrid-expander").remove(),$expander=e('<span class="treegrid-expander">').prependTo($container),t.data("count")&&(t.hasClass("expanded")?$expander.addClass("treegrid-expander-expanded"):$expander.addClass("treegrid-expander-collapsed")),$container.css("marginLeft",n*$expander.width()),h(t)&&t.hide(),f(r,n+1,a)})}function p(){var t=e(this);(t.hasClass("treegrid-expander-expanded")||t.hasClass("treegrid-expander-collapsed"))&&S.toggle.call(t.closest("tr"))}function h(e){if(null===l(e))return!1;var t=i(e);return c(t)?!0:h(t)}function g(t){var n=t.closest("table").data("treegrid-settings");return o(t).not(t).remove(),e.isFunction(n.source)&&!t.hasClass("loading")&&(t.addClass("loading"),n.source.call(t,s(t),function(e){t.removeData("loadNeeded").data("loaded",!0),r(t,e),t.removeClass("loading"),S.expand.call(t)})),"string"!==e.type(n.source)||t.hasClass("loading")||(t.addClass("loading"),e.post(n.source,{id:s(t)},function(e){t.removeDate("loadNeeded").data("loaded",!0),r(t,e),t.removeClass("loading")},"json")),!1}function v(t){if(0===t.button){var n=e(this),a=n.closest("table").data("treegrid-settings");if(a.enableMove){var r=e(t.target);if(!r.hasClass("treegrid-expander")&&(a.moveHandle===!1||n.find(a.moveHandle)[0]==r[0])){B=n.closest("tr"),X=t.pageX,Y=t.pageY;var i=n.offset();return j=i.left-t.pageX,D=i.top-t.pageY,e(document).on("mouseup",m).on("mousemove",x),!1}}}}function m(t){R&&b(),e(document).off("mouseup",m).off("mousemove",x)}function x(e){var t=Math.max(Math.abs(e.pageX-X),Math.abs(e.pageY-Y)),n=B.closest("table").data("treegrid-settings");t>=n.moveDistance&&!R?C(e):R&&y(e)}function C(t){if(R=!0,T(),F=B.find(">td:first>.treegrid-container").clone().addClass("dragging").css({left:t.pageX+j,top:t.pageY+D}),F.find(">.treegrid-expander").remove(),F.appendTo("body"),!e(".treegrid-move-indicator").length){N=e('<div class="treegrid-move-indicator">').appendTo("body");var n=B.closest("table"),a=n.data("treegrid-settings");a.onMoveStart.call(n,B,F)}}function b(){R=!1,L!==!1&&(window.clearTimeout(L),L=!1),F.remove(),N.remove();var e=B.closest("table"),t=e.data("treegrid-settings");t.onMoveStop.call(e,B),k&&w()}function y(e){F.css({left:e.pageX+j,top:e.pageY+D});var t=A(e.pageX,e.pageY);(H!==t.node||E!==t.position)&&(null!==H&&M(),H=t.node,E=t.position,null!==H&&$(t))}function $(e){if(1==e.position){var t=e.node;L===!1&&c(t)&&(L=window.setTimeout(function(){S.expand.call(t),L=!1},500))}else L!==!1&&(window.clearTimeout(L),L=!1);k=!0;var n=B.closest("table"),a=n.data("treegrid-settings");a.onMoveOver.call(n,B,F,e.node,e.position)===!1&&(k=!1),k&&N.css({display:"block",left:e.node.find(">td:first>.treegrid-container").offset().left,top:e.top})}function M(){L!==!1&&(window.clearTimeout(L),L=!1),k=!1,N.hide();var e=B.closest("table"),t=e.data("treegrid-settings");t.onMoveOut.call(e,B,F,H)}function w(){M();var e=B.closest("table"),t=e.data("treegrid-settings"),n=t.onMove.call(e,B,H,E);n!==!1&&S.move.call(B,H,E)}function T(){var t=[];o(B).each(function(){t.push(s(e(this)))}),O=[],B.parent().find("tr").each(function(){var n=e(this),a=s(n);if(null!==a&&-1===e.inArray(a,t)){var r=n.offset();O.push([r.left,r.top,n.width(),n.height(),n])}})}function A(t,n){var a=null,r={node:null,position:null,top:null};if(e.each(O,function(e,i){return t>=i[0]&&n>=i[1]&&t<=i[0]+i[2]&&n<=i[1]+i[3]?(a=i,r.node=i[4],!1):void 0}),null!==a){var i=a[3]/4,d=3*i,o=n-a[1];i>o?(r.position=0,r.top=a[1]):o>=d?(r.position=2,r.top=a[1]+a[3]):(r.position=1,r.top=a[1]+a[3]/2)}return r}e.fn.treegrid=function(n){return S[n]?S[n].apply(this,Array.prototype.slice.call(arguments,1)):"object"!=typeof n&&n?void e.error("Method with name "+n+" does not exists for jQuery.treegrid"):t.apply(this,arguments)},e.fn.treegrid.defaults={source:null,enableMove:!1,moveDistance:10,moveHandle:!1,onExpand:function(){return!0},onCollapse:function(){return!0},onAdd:function(){},onMoveStart:function(){},onMoveStop:function(){},onMoveOver:function(){return!0},onMoveOut:function(){},onMove:function(){return!0}};var N,X,Y,j,D,O,E,S={option:function(t,n){var a=this.data("treegrid-settings");return"string"===e.type(t)&&void 0!==n&&(t={optionName:n}),e.isPlainObject(t)?(a=e.extend({},a,t),this.data("treegrid-settings",a)):"string"===e.type(t)?a[t]:a},getId:function(){return s(this)},getDepth:function(){return this.data("treegrid-depth")},toggle:function(){return this.each(function(){$this=e(this),u($this)?S.collapse.call($this):c($this)&&S.expand.call($this)})},expand:function(){var t=!1;return this.each(function(){var a=e(this);c(a)&&(a.data("loaded")||g(a))&&(n(a),t=!0)}),R&&T(),this},collapse:function(){var t=!1;return this.each(function(){var n=e(this);u(n)&&(a(n),t=!0)}),R&&T(),this},add:function(t){return this.each(function(){r(e(this),t)}),R&&T(),this},remove:function(){return this.each(function(){var t=e(this),n=i(t);o(t).remove(),f(n,n.data("treegrid-depth"))}),R&&T(),this},move:function(e,t){var n,a,r,d=i(this);if(r=o(this),n=0==t?e.prev():o(e).not(r).last(),-1==r.index(n)){a=1===t?s(e):l(e),this.removeClass("treegrid-parent-"+l(this)),null!==a&&this.addClass("treegrid-parent-"+a),n.length?r.insertAfter(n):r.prependTo(this.parent()),h(this)&&r.hide(),d.length&&f(d,d.data("treegrid-depth"));var c=i(this);c.length?f(c,c.data("treegrid-depth")):f(this)}},getRoots:function(){var t=[];return this.find(">tbody>tr").each(function(){null===l(e(this))&&t.push(this)}),e(t)},getChildNodes:function(){var t=e();return this.each(function(){t=t.add(d(e(this)))}),t},getBranch:function(){var t=e();return this.each(function(){t=t.add(o(e(this)))}),t},getParent:function(){var t=e();return this.each(function(){t=t.add(i(e(this)))}),t},isCollapsed:function(){var t=!1;return this.each(function(){return t=c(e(this)),t?!1:void 0}),t},isExpanded:function(){var t=!1;return this.each(function(){return t=u(e(this)),t?!1:void 0}),t}},B=null,F=null,H=null,R=!1,k=!1,L=!1}(jQuery);